name: Test

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

jobs:
  laravel-tests:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: shivammathur/setup-php@15c43e89cdef867065b0213be354c2841860869e
        with:
          php-version: "8.2"
          extensions: ctype, curl, dom, fileinfo, filter, hash, intl, json, libxml, mbstring, openssl, pcre, pdo, pdo_sqlite, session, tokenizer, xml, xmlwriter
      - uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
      - name: Copy .env
        run: php -r "file_exists('.env') || copy('.env.example', '.env');"
      - name: Install PHP Dependencies
        run: |
          composer --version
          php --version
          composer update -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
      - name: Install Node Dependencies
        run: npm ci
      - name: Generate key
        run: php artisan key:generate
      - name: Run Filament upgrade
        run: php artisan filament:upgrade
      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache
      - name: Create Database
        run: |
          mkdir -p database
          touch database/database.sqlite
      - name: Execute tests (Unit and Feature tests) via PHPUnit/Pest
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: database/database.sqlite
        run: php artisan test
